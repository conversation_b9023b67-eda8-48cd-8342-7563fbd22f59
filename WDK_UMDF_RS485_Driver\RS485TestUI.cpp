//
// RS485 UMDF Driver Test UI - Enhanced Testing Application
// Tests RS485 UMDF driver functionality with comprehensive interface
//

#include <windows.h>
#include <commctrl.h>
#include <setupapi.h>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "setupapi.lib")

// Window controls IDs
#define ID_COMBO_PORT       1001
#define ID_BUTTON_CONNECT   1002
#define ID_BUTTON_DISCONNECT 1003
#define ID_EDIT_SEND        1004
#define ID_BUTTON_SEND      1005
#define ID_EDIT_RECEIVE     1006
#define ID_BUTTON_CLEAR     1007
#define ID_BUTTON_REFRESH   1008
#define ID_COMBO_BAUDRATE   1009
#define ID_BUTTON_TEST_S001 1010
#define ID_BUTTON_TEST_A001 1011
#define ID_EDIT_SLAVE_ID    1012
#define ID_BUTTON_TEST_DRIVER 1013
#define ID_BUTTON_TEST_U001 1014
#define ID_BUTTON_AUTO_TEST 1015
#define ID_STATIC_STATUS    1016
#define ID_PROGRESS_BAR     1017
#define ID_BUTTON_TEST_S002 1018
#define ID_BUTTON_TEST_U002 1019
#define ID_BUTTON_TEST_U003 1020
#define ID_BUTTON_TEST_U004 1021
#define ID_BUTTON_TEST_U005 1022
#define ID_BUTTON_TEST_U006 1023
#define ID_BUTTON_CONNECTION_TEST 1024
#define ID_BUTTON_TEST_S002 1018
#define ID_BUTTON_TEST_U002 1019
#define ID_BUTTON_TEST_U003 1020
#define ID_BUTTON_TEST_U004 1021
#define ID_BUTTON_TEST_U005 1022
#define ID_BUTTON_TEST_U006 1023
#define ID_BUTTON_CONNECTION_TEST 1024

class RS485TestApp {
private:
    HWND m_hWnd;
    HWND m_hComboPort;
    HWND m_hComboBaudRate;
    HWND m_hEditSend;
    HWND m_hEditReceive;
    HWND m_hEditSlaveId;
    HWND m_hButtonConnect;
    HWND m_hButtonDisconnect;
    HWND m_hStaticStatus;
    HWND m_hProgressBar;
    HANDLE m_hComPort;
    HANDLE m_hDriverHandle;
    bool m_bConnected;
    bool m_bDriverConnected;

public:
    RS485TestApp() : m_hWnd(nullptr), m_hComPort(INVALID_HANDLE_VALUE),
                     m_hDriverHandle(INVALID_HANDLE_VALUE), m_bConnected(false),
                     m_bDriverConnected(false) {}

    ~RS485TestApp() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
        }
        if (m_hDriverHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hDriverHandle);
        }
    }

    bool CreateMainWindow(HINSTANCE hInstance) {
        // Register window class
        WNDCLASSEX wc = {0};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = L"RS485TestWindow";
        wc.cbWndExtra = sizeof(RS485TestApp*);

        if (!RegisterClassEx(&wc)) return false;

        // Create main window
        m_hWnd = CreateWindowEx(
            0,
            L"RS485TestWindow",
            L"RS485 UMDF Driver Test Tool - Enhanced Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 900, 700,
            nullptr, nullptr, hInstance, this
        );

        if (!m_hWnd) return false;

        ShowWindow(m_hWnd, SW_SHOW);
        UpdateWindow(m_hWnd);
        return true;
    }

    void CreateControls() {
        // COM Port selection
        CreateWindow(L"STATIC", L"COM Port:", WS_VISIBLE | WS_CHILD,
            10, 10, 80, 20, m_hWnd, nullptr, nullptr, nullptr);
        
        m_hComboPort = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            100, 10, 100, 200, m_hWnd, (HMENU)ID_COMBO_PORT, nullptr, nullptr);

        // Baud Rate selection
        CreateWindow(L"STATIC", L"Baud Rate:", WS_VISIBLE | WS_CHILD,
            220, 10, 80, 20, m_hWnd, nullptr, nullptr, nullptr);
        
        m_hComboBaudRate = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            310, 10, 100, 200, m_hWnd, (HMENU)ID_COMBO_BAUDRATE, nullptr, nullptr);

        // Slave ID
        CreateWindow(L"STATIC", L"Slave ID:", WS_VISIBLE | WS_CHILD,
            430, 10, 60, 20, m_hWnd, nullptr, nullptr, nullptr);
        
        m_hEditSlaveId = CreateWindow(L"EDIT", L"1",
            WS_VISIBLE | WS_CHILD | WS_BORDER,
            500, 10, 50, 20, m_hWnd, (HMENU)ID_EDIT_SLAVE_ID, nullptr, nullptr);

        // Control buttons
        CreateWindow(L"BUTTON", L"Refresh Ports", WS_VISIBLE | WS_CHILD,
            570, 10, 100, 25, m_hWnd, (HMENU)ID_BUTTON_REFRESH, nullptr, nullptr);

        m_hButtonConnect = CreateWindow(L"BUTTON", L"Connect", WS_VISIBLE | WS_CHILD,
            10, 50, 80, 30, m_hWnd, (HMENU)ID_BUTTON_CONNECT, nullptr, nullptr);

        m_hButtonDisconnect = CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
            100, 50, 80, 30, m_hWnd, (HMENU)ID_BUTTON_DISCONNECT, nullptr, nullptr);
        EnableWindow(m_hButtonDisconnect, FALSE);

        // Driver test button
        CreateWindow(L"BUTTON", L"Test Driver", WS_VISIBLE | WS_CHILD,
            200, 50, 100, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_DRIVER, nullptr, nullptr);

        // Protocol test buttons - Row 1
        CreateWindow(L"BUTTON", L"Test S001", WS_VISIBLE | WS_CHILD,
            310, 50, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_S001, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Test S002", WS_VISIBLE | WS_CHILD,
            400, 50, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_S002, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Test A001", WS_VISIBLE | WS_CHILD,
            490, 50, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_A001, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Auto Test", WS_VISIBLE | WS_CHILD,
            580, 50, 80, 30, m_hWnd, (HMENU)ID_BUTTON_AUTO_TEST, nullptr, nullptr);

        // Protocol test buttons - Row 2 (U-series commands)
        CreateWindow(L"BUTTON", L"Test U001", WS_VISIBLE | WS_CHILD,
            310, 90, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_U001, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Test U002", WS_VISIBLE | WS_CHILD,
            400, 90, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_U002, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Test U003", WS_VISIBLE | WS_CHILD,
            490, 90, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_U003, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Test U004", WS_VISIBLE | WS_CHILD,
            580, 90, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_U004, nullptr, nullptr);

        // Protocol test buttons - Row 3 (GPIO commands and connection test)
        CreateWindow(L"BUTTON", L"Test U005", WS_VISIBLE | WS_CHILD,
            310, 130, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_U005, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Test U006", WS_VISIBLE | WS_CHILD,
            400, 130, 80, 30, m_hWnd, (HMENU)ID_BUTTON_TEST_U006, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"RS485 Test", WS_VISIBLE | WS_CHILD,
            490, 130, 80, 30, m_hWnd, (HMENU)ID_BUTTON_CONNECTION_TEST, nullptr, nullptr);

        // Status display
        CreateWindow(L"STATIC", L"Status:", WS_VISIBLE | WS_CHILD,
            10, 170, 50, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hStaticStatus = CreateWindow(L"STATIC", L"Ready", WS_VISIBLE | WS_CHILD | SS_SUNKEN,
            70, 170, 600, 20, m_hWnd, (HMENU)ID_STATIC_STATUS, nullptr, nullptr);

        // Progress bar
        m_hProgressBar = CreateWindow(PROGRESS_CLASS, nullptr,
            WS_VISIBLE | WS_CHILD | PBS_SMOOTH,
            680, 170, 150, 20, m_hWnd, (HMENU)ID_PROGRESS_BAR, nullptr, nullptr);
        SendMessage(m_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));

        // Send data section
        CreateWindow(L"STATIC", L"Send Data (Hex):", WS_VISIBLE | WS_CHILD,
            10, 200, 120, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hEditSend = CreateWindow(L"EDIT", L"AA 01 54 45 53 54 00 00 00 00 00 00 00 00 00 0D",
            WS_VISIBLE | WS_CHILD | WS_BORDER,
            10, 220, 700, 25, m_hWnd, (HMENU)ID_EDIT_SEND, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Send", WS_VISIBLE | WS_CHILD,
            720, 220, 60, 25, m_hWnd, (HMENU)ID_BUTTON_SEND, nullptr, nullptr);

        // Receive data section
        CreateWindow(L"STATIC", L"Test Results & Received Data:", WS_VISIBLE | WS_CHILD,
            10, 260, 200, 20, m_hWnd, nullptr, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Clear", WS_VISIBLE | WS_CHILD,
            720, 260, 60, 25, m_hWnd, (HMENU)ID_BUTTON_CLEAR, nullptr, nullptr);

        m_hEditReceive = CreateWindow(L"EDIT", nullptr,
            WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
            10, 285, 770, 370, m_hWnd, (HMENU)ID_EDIT_RECEIVE, nullptr, nullptr);

        // Initialize combo boxes
        RefreshPorts();
        InitializeBaudRates();
    }

    void RefreshPorts() {
        SendMessage(m_hComboPort, CB_RESETCONTENT, 0, 0);

        // Enumerate COM ports
        for (int i = 1; i <= 20; i++) {
            wchar_t portName[20];
            swprintf_s(portName, L"COM%d", i);

            HANDLE hPort = CreateFile(portName, GENERIC_READ | GENERIC_WRITE,
                0, nullptr, OPEN_EXISTING, 0, nullptr);

            if (hPort != INVALID_HANDLE_VALUE) {
                SendMessage(m_hComboPort, CB_ADDSTRING, 0, (LPARAM)portName);
                CloseHandle(hPort);
            }
        }

        // Also check for FTDI devices
        CheckFTDIDevices();

        SendMessage(m_hComboPort, CB_SETCURSEL, 0, 0);
    }

    void CheckFTDIDevices() {
        AppendToReceiveBox(L"Checking for FTDI devices...");

        HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
            nullptr, L"FTDIBUS", nullptr, DIGCF_PRESENT | DIGCF_ALLCLASSES);

        if (deviceInfoSet != INVALID_HANDLE_VALUE) {
            SP_DEVINFO_DATA deviceInfoData;
            deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

            for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
                wchar_t deviceDesc[256];
                if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData,
                    SPDRP_DEVICEDESC, nullptr, (PBYTE)deviceDesc, sizeof(deviceDesc), nullptr)) {

                    std::wstring desc(deviceDesc);
                    if (desc.find(L"FTDI") != std::wstring::npos ||
                        desc.find(L"USB Serial") != std::wstring::npos) {
                        AppendToReceiveBox(L"Found FTDI device: " + desc);
                    }
                }
            }
            SetupDiDestroyDeviceInfoList(deviceInfoSet);
        }
    }

    void InitializeBaudRates() {
        const wchar_t* baudRates[] = {L"9600", L"19200", L"38400", L"57600", L"115200"};
        
        for (const auto& rate : baudRates) {
            SendMessage(m_hComboBaudRate, CB_ADDSTRING, 0, (LPARAM)rate);
        }
        
        SendMessage(m_hComboBaudRate, CB_SETCURSEL, 0, 0); // Default to 9600
    }

    void ConnectToPort() {
        if (m_bConnected) return;

        wchar_t portName[20];
        int sel = SendMessage(m_hComboPort, CB_GETCURSEL, 0, 0);
        SendMessage(m_hComboPort, CB_GETLBTEXT, sel, (LPARAM)portName);

        std::wstring fullPortName = L"\\\\.\\" + std::wstring(portName);
        
        m_hComPort = CreateFile(fullPortName.c_str(),
            GENERIC_READ | GENERIC_WRITE, 0, nullptr,
            OPEN_EXISTING, 0, nullptr);

        if (m_hComPort == INVALID_HANDLE_VALUE) {
            MessageBox(m_hWnd, L"Failed to open COM port!", L"Error", MB_OK | MB_ICONERROR);
            return;
        }

        // Configure port
        DCB dcb = {0};
        dcb.DCBlength = sizeof(DCB);
        GetCommState(m_hComPort, &dcb);

        // Get baud rate
        wchar_t baudRateStr[20];
        sel = SendMessage(m_hComboBaudRate, CB_GETCURSEL, 0, 0);
        SendMessage(m_hComboBaudRate, CB_GETLBTEXT, sel, (LPARAM)baudRateStr);
        dcb.BaudRate = _wtoi(baudRateStr);

        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;

        SetCommState(m_hComPort, &dcb);

        // Set timeouts
        COMMTIMEOUTS timeouts = {0};
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 1000;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        SetCommTimeouts(m_hComPort, &timeouts);

        m_bConnected = true;
        EnableWindow(m_hButtonConnect, FALSE);
        EnableWindow(m_hButtonDisconnect, TRUE);
        
        AppendToReceiveBox(L"Connected to " + std::wstring(portName));
    }

    void DisconnectFromPort() {
        if (!m_bConnected) return;

        CloseHandle(m_hComPort);
        m_hComPort = INVALID_HANDLE_VALUE;
        m_bConnected = false;

        EnableWindow(m_hButtonConnect, TRUE);
        EnableWindow(m_hButtonDisconnect, FALSE);
        
        AppendToReceiveBox(L"Disconnected");
    }

    void TestDriverConnection() {
        UpdateStatus(L"Testing RS485 UMDF Driver...");
        SetProgress(10);

        AppendToReceiveBox(L"Testing RS485 UMDF Driver Connection...");

        // Try to open the RS485 filter driver
        const wchar_t* driverPaths[] = {
            L"\\\\.\\RS485FilterDriver",
            L"\\\\.\\RS485Driver",
            L"\\\\.\\Global\\RS485FilterDriver"
        };

        bool driverFound = false;
        for (const auto& path : driverPaths) {
            m_hDriverHandle = CreateFile(path,
                GENERIC_READ | GENERIC_WRITE,
                FILE_SHARE_READ | FILE_SHARE_WRITE,
                nullptr, OPEN_EXISTING, 0, nullptr);

            if (m_hDriverHandle != INVALID_HANDLE_VALUE) {
                AppendToReceiveBox(L"RS485 Driver found at: " + std::wstring(path));
                m_bDriverConnected = true;
                driverFound = true;
                SetProgress(50);
                break;
            }
        }

        if (!driverFound) {
            AppendToReceiveBox(L"RS485 Driver not accessible");
            AppendToReceiveBox(L"Possible solutions:");
            AppendToReceiveBox(L"   1. Check if driver is properly installed");
            AppendToReceiveBox(L"   2. Run as Administrator");
            AppendToReceiveBox(L"   3. Enable test signing: bcdedit /set testsigning on");
            SetProgress(0);
            UpdateStatus(L"Driver test failed");
            return;
        }

        // Test basic driver functionality
        TestDriverIOCTL();
        SetProgress(100);
        UpdateStatus(L"Driver test completed");
    }

    void TestDriverIOCTL() {
        if (m_hDriverHandle == INVALID_HANDLE_VALUE) {
            AppendToReceiveBox(L"Driver handle not available");
            return;
        }

        AppendToReceiveBox(L"Testing driver IOCTL interface...");

        // Test basic IOCTL calls
        DWORD bytesReturned;
        BYTE testBuffer[16] = {0};

        // Try a simple device information query
        BOOL result = DeviceIoControl(m_hDriverHandle,
            CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS),
            nullptr, 0, testBuffer, sizeof(testBuffer), &bytesReturned, nullptr);

        if (result) {
            AppendToReceiveBox(L"Driver IOCTL interface is working");
            AppendToReceiveBox(L"Returned " + std::to_wstring(bytesReturned) + L" bytes");
        } else {
            DWORD error = GetLastError();
            AppendToReceiveBox(L"IOCTL test returned error: " + std::to_wstring(error));
            AppendToReceiveBox(L"This may be normal for development drivers");
        }
    }

    void SendData() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        wchar_t sendText[1024];
        GetWindowText(m_hEditSend, sendText, sizeof(sendText)/sizeof(wchar_t));

        // Parse hex string
        std::vector<BYTE> data = ParseHexString(sendText);
        if (data.empty()) {
            MessageBox(m_hWnd, L"Invalid hex data format!", L"Error", MB_OK | MB_ICONERROR);
            return;
        }

        DWORD bytesWritten;
        if (WriteFile(m_hComPort, data.data(), data.size(), &bytesWritten, nullptr)) {
            AppendToReceiveBox(L"Sent: " + FormatHexData(data));

            // Try to read response
            BYTE buffer[256];
            DWORD bytesRead;
            Sleep(100); // Wait for response

            if (ReadFile(m_hComPort, buffer, sizeof(buffer), &bytesRead, nullptr) && bytesRead > 0) {
                std::vector<BYTE> response(buffer, buffer + bytesRead);
                AppendToReceiveBox(L"Received: " + FormatHexData(response));
            }
        } else {
            AppendToReceiveBox(L"Send failed!");
        }
    }

    void TestS001Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // S001 command: Set slave address
        BYTE s001Command[] = {
            0xAA,                           // Header
            0x01,                           // Current ID
            'S', '0', '0', '1',            // S001 command
            0x00, 0x00, 0x00, (BYTE)slaveId, // New slave ID
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(s001Command, s001Command + sizeof(s001Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        SendData();
    }

    void TestA001Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // A001 command: Query data
        BYTE a001Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'A', '0', '0', '1',            // A001 command
            0x00, 0x00, 0x00, 0x00,        // Query parameters
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(a001Command, a001Command + sizeof(a001Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        SendData();
    }

    void TestU001Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing U001 command (SEL Threshold)...");

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // U001 command: Set SEL threshold to 250mA
        BYTE u001Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'U', '0', '0', '1',            // U001 command
            0xFA, 0x00, 0x00, 0x00,        // 250 (0x00FA) in little-endian
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(u001Command, u001Command + sizeof(u001Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending U001 command (SEL Threshold = 250mA)");
        SendData();
    }

    void TestS002Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing S002 command (Baud Rate)...");

        // S002 command: Set baud rate to 115200
        BYTE s002Command[] = {
            0xAA,                           // Header
            0x00,                           // Broadcast address
            'S', '0', '0', '2',            // S002 command
            0x00, 0xC2, 0x01, 0x00,        // 115200 (0x0001C200) in little-endian
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(s002Command, s002Command + sizeof(s002Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending S002 command (Baud Rate = 115200)");
        SendData();
    }

    void TestU002Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing U002 command (Max Amplitude)...");

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // U002 command: Set max amplitude threshold to 1500mA
        BYTE u002Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'U', '0', '0', '2',            // U002 command
            0xDC, 0x05, 0x00, 0x00,        // 1500 (0x000005DC) in little-endian
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(u002Command, u002Command + sizeof(u002Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending U002 command (Max Amplitude = 1500mA)");
        SendData();
    }

    void TestU003Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing U003 command (SEL Detection Count)...");

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // U003 command: Set SEL detection count to 3
        BYTE u003Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'U', '0', '0', '3',            // U003 command
            0x03, 0x00, 0x00, 0x00,        // 3 in little-endian
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(u003Command, u003Command + sizeof(u003Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending U003 command (SEL Detection Count = 3)");
        SendData();
    }

    void TestU004Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing U004 command (Power Cycle Duration)...");

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // U004 command: Set power cycle duration to 600ms
        BYTE u004Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'U', '0', '0', '4',            // U004 command
            0x58, 0x02, 0x00, 0x00,        // 600 (0x00000258) in little-endian
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(u004Command, u004Command + sizeof(u004Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending U004 command (Power Cycle Duration = 600ms)");
        SendData();
    }

    void TestU005Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing U005 command (GPIO Input Enable)...");

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // U005 command: Enable GPIO input channel 0
        // Format: Lower 32 bits = channel (0), Upper 32 bits = enable (1)
        BYTE u005Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'U', '0', '0', '5',            // U005 command
            0x00, 0x00, 0x00, 0x00,        // Channel 0 (lower 32 bits)
            0x01, 0x00, 0x00, 0x00,        // Enable flag 1 (upper 32 bits)
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(u005Command, u005Command + sizeof(u005Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending U005 command (Enable GPIO Input Channel 0)");
        SendData();
    }

    void TestU006Command() {
        if (!m_bConnected) {
            MessageBox(m_hWnd, L"Please connect to a port first!", L"Error", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"Testing U006 command (GPIO Output Enable)...");

        wchar_t slaveIdStr[10];
        GetWindowText(m_hEditSlaveId, slaveIdStr, sizeof(slaveIdStr)/sizeof(wchar_t));
        int slaveId = _wtoi(slaveIdStr);

        // U006 command: Enable GPIO output channel 1
        // Format: Lower 32 bits = channel (1), Upper 32 bits = enable (1)
        BYTE u006Command[] = {
            0xAA,                           // Header
            (BYTE)slaveId,                  // Slave ID
            'U', '0', '0', '6',            // U006 command
            0x01, 0x00, 0x00, 0x00,        // Channel 1 (lower 32 bits)
            0x01, 0x00, 0x00, 0x00,        // Enable flag 1 (upper 32 bits)
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(u006Command, u006Command + sizeof(u006Command));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending U006 command (Enable GPIO Output Channel 1)");
        SendData();
    }

    void TestRS485Connection() {
        AppendToReceiveBox(L"=== RS485 Connection Test ===");

        if (!m_bConnected) {
            AppendToReceiveBox(L"ERROR: No COM port connected!");
            AppendToReceiveBox(L"Please connect to a COM port first.");
            MessageBox(m_hWnd, L"Please connect to a COM port first!", L"Connection Test Failed", MB_OK | MB_ICONWARNING);
            return;
        }

        AppendToReceiveBox(L"✓ COM port connection: OK");

        // Test basic communication by sending a simple test frame
        AppendToReceiveBox(L"Testing RS485 communication...");

        // Send a basic test frame to verify RS485 communication
        BYTE testFrame[] = {
            0xAA,                           // Header
            0x01,                           // Test slave ID
            'T', 'E', 'S', 'T',            // TEST command
            0x00, 0x00, 0x00, 0x00,        // Test data
            0x00, 0x00, 0x00, 0x00,        // Padding
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        std::vector<BYTE> data(testFrame, testFrame + sizeof(testFrame));
        SetWindowText(m_hEditSend, FormatHexData(data).c_str());
        AppendToReceiveBox(L"Sending RS485 test frame...");

        if (SendData()) {
            AppendToReceiveBox(L"✓ RS485 test frame sent successfully");
            AppendToReceiveBox(L"✓ RS485 connection appears to be working");
            AppendToReceiveBox(L"Note: FPGA response depends on FPGA firmware implementation");
            UpdateStatus(L"RS485 connection test completed - Ready for FPGA testing");
        } else {
            AppendToReceiveBox(L"✗ Failed to send RS485 test frame");
            AppendToReceiveBox(L"✗ RS485 connection may have issues");
            UpdateStatus(L"RS485 connection test failed");
        }

        AppendToReceiveBox(L"=== Connection Test Complete ===");
    }

    void RunAutoTest() {
        AppendToReceiveBox(L"Starting Automatic Test Sequence...");
        UpdateStatus(L"Running automatic tests...");
        SetProgress(0);

        // Test 1: Driver Connection
        AppendToReceiveBox(L"\r\n=== Test 1: Driver Connection ===");
        TestDriverConnection();
        ProcessMessages();
        Sleep(500);
        SetProgress(25);

        // Test 2: Port Connection
        AppendToReceiveBox(L"\r\n=== Test 2: Port Connection ===");
        if (!m_bConnected) {
            ConnectToPort();
        }
        ProcessMessages();
        Sleep(500);
        SetProgress(50);

        // Test 3: S001 Command
        AppendToReceiveBox(L"\r\n=== Test 3: S001 Command Test ===");
        TestS001Command();
        ProcessMessages();
        Sleep(1000);
        SetProgress(75);

        // Test 4: A001 Command
        AppendToReceiveBox(L"\r\n=== Test 4: A001 Command Test ===");
        TestA001Command();
        ProcessMessages();
        Sleep(1000);
        SetProgress(100);

        AppendToReceiveBox(L"\r\nAutomatic test sequence completed!");
        UpdateStatus(L"Auto test completed");
    }

    void UpdateStatus(const std::wstring& status) {
        SetWindowText(m_hStaticStatus, status.c_str());
    }

    void SetProgress(int percent) {
        SendMessage(m_hProgressBar, PBM_SETPOS, percent, 0);
    }

    void ProcessMessages() {
        MSG msg;
        while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        UpdateWindow(m_hWnd);
    }

    std::vector<BYTE> ParseHexString(const std::wstring& hexStr) {
        std::vector<BYTE> result;
        std::wistringstream iss(hexStr);
        std::wstring token;

        while (iss >> token) {
            if (token.length() == 2) {
                try {
                    BYTE value = (BYTE)std::stoi(token, nullptr, 16);
                    result.push_back(value);
                } catch (...) {
                    return std::vector<BYTE>(); // Return empty on error
                }
            }
        }
        return result;
    }

    std::wstring FormatHexData(const std::vector<BYTE>& data) {
        std::wostringstream oss;
        for (size_t i = 0; i < data.size(); ++i) {
            if (i > 0) oss << L" ";
            oss << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << data[i];
        }
        return oss.str();
    }

    void AppendToReceiveBox(const std::wstring& text) {
        // Get current text
        int len = GetWindowTextLength(m_hEditReceive);
        SendMessage(m_hEditReceive, EM_SETSEL, len, len);
        
        // Add timestamp
        SYSTEMTIME st;
        GetLocalTime(&st);
        wchar_t timestamp[100];
        swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);
        
        std::wstring fullText = timestamp + text + L"\r\n";
        SendMessage(m_hEditReceive, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());
        
        // Scroll to bottom
        SendMessage(m_hEditReceive, EM_SCROLLCARET, 0, 0);
    }

    void ClearReceiveBox() {
        SetWindowText(m_hEditReceive, L"");
    }

    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        RS485TestApp* pApp = nullptr;

        if (uMsg == WM_CREATE) {
            CREATESTRUCT* pCreate = (CREATESTRUCT*)lParam;
            pApp = (RS485TestApp*)pCreate->lpCreateParams;
            SetWindowLongPtr(hWnd, 0, (LONG_PTR)pApp);
            pApp->m_hWnd = hWnd;
            pApp->CreateControls();
        } else {
            pApp = (RS485TestApp*)GetWindowLongPtr(hWnd, 0);
        }

        if (pApp) {
            switch (uMsg) {
            case WM_COMMAND:
                switch (LOWORD(wParam)) {
                case ID_BUTTON_REFRESH:
                    pApp->RefreshPorts();
                    break;
                case ID_BUTTON_CONNECT:
                    pApp->ConnectToPort();
                    break;
                case ID_BUTTON_DISCONNECT:
                    pApp->DisconnectFromPort();
                    break;
                case ID_BUTTON_SEND:
                    pApp->SendData();
                    break;
                case ID_BUTTON_CLEAR:
                    pApp->ClearReceiveBox();
                    break;
                case ID_BUTTON_TEST_S001:
                    pApp->TestS001Command();
                    break;
                case ID_BUTTON_TEST_S002:
                    pApp->TestS002Command();
                    break;
                case ID_BUTTON_TEST_A001:
                    pApp->TestA001Command();
                    break;
                case ID_BUTTON_TEST_DRIVER:
                    pApp->TestDriverConnection();
                    break;
                case ID_BUTTON_TEST_U001:
                    pApp->TestU001Command();
                    break;
                case ID_BUTTON_TEST_U002:
                    pApp->TestU002Command();
                    break;
                case ID_BUTTON_TEST_U003:
                    pApp->TestU003Command();
                    break;
                case ID_BUTTON_TEST_U004:
                    pApp->TestU004Command();
                    break;
                case ID_BUTTON_TEST_U005:
                    pApp->TestU005Command();
                    break;
                case ID_BUTTON_TEST_U006:
                    pApp->TestU006Command();
                    break;
                case ID_BUTTON_CONNECTION_TEST:
                    pApp->TestRS485Connection();
                    break;
                case ID_BUTTON_AUTO_TEST:
                    pApp->RunAutoTest();
                    break;
                }
                break;

            case WM_CLOSE:
                pApp->DisconnectFromPort();
                if (pApp->m_hDriverHandle != INVALID_HANDLE_VALUE) {
                    CloseHandle(pApp->m_hDriverHandle);
                    pApp->m_hDriverHandle = INVALID_HANDLE_VALUE;
                }
                DestroyWindow(hWnd);
                break;

            case WM_DESTROY:
                PostQuitMessage(0);
                break;
            }
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    InitCommonControls();

    RS485TestApp app;
    if (!app.CreateMainWindow(hInstance)) {
        MessageBox(nullptr, L"Failed to create window!", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
